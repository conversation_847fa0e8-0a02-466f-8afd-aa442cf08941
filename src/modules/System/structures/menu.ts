import { loadable } from '../../../common/utils/lazyLoading';

const StorageManagementPage = loadable(
  () => import('../StorageManagementPage'),
);
const ProgramsSetupPage = loadable(() => import('../ProgramsSetupPage'));

const LoginHistoryPage = loadable(() => import('../LoginHistoryPage'));
const LoginInvitesPage = loadable(() => import('../LoginInvitesPage'));

const EmailGatewayValidationPage = loadable(
  () => import('../EmailGatewayValidationPage'),
);
const TextToAudioUsageHistoryPage = loadable(
  () => import('../TextToAudioUsageHistoryPage'),
);
const AiImageGenerationPage = loadable(
  () => import('../AiImageGenerationPage'),
);
const ResetPasswordRequestsPage = loadable(
  () => import('../ResetPasswordRequests'),
);

export default [
  {
    id: 'master_log',
    title: 'Master Log',
    route: 'master-log',
    permission: 'master_log',
    children: [
      {
        id: 'login_invites',
        title: 'Login Invites',
        route: 'login-invites',
        permission: 'master_log.login_invites',
        component: LoginInvitesPage,
      },
      {
        id: 'login_history',
        title: 'Login History',
        route: 'login-history',
        permission: 'master_log.login_history',
        component: LoginHistoryPage,
      },
      {
        id: 'reset_password_requests',
        title: 'Reset Password Requests',
        route: 'reset-password-requests',
        permission: 'master_log.reset_password_requests',
        component: ResetPasswordRequestsPage,
      },
      {
        id: 'email_gateway_validation',
        title: 'Email Gateway Validation',
        route: 'email-gateway-validation',
        permission: 'master_log.email_gateway_validation',
        component: EmailGatewayValidationPage,
      },
      {
        id: 'text_to_audio_usage_history',
        title: 'Text-To-Audio Usage History',
        route: 'text-to-audio-usage-history',
        permission: 'master_log.email_gateway_validation',
        // permission: 'master_log.text_to_audio_usage_history',
        component: TextToAudioUsageHistoryPage,
      },
      {
        id: 'ai_image_generation',
        title: 'AI Image Generation',
        route: 'ai_image_generation',
        permission: 'master_log.email_gateway_validation',
        // permission: 'master_log.ai_image_generation',
        component: AiImageGenerationPage,
      },
    ],
  },
  {
    id: 'storage_management',
    title: 'Storage Management',
    route: 'storage-management',
    component: StorageManagementPage,
    permission: 'storage_management',
  },
  {
    id: 'programs_setup',
    title: 'Programs Setup',
    route: 'programs-setup',
    component: ProgramsSetupPage,
    // permission: 'storage_management',
  },
];
