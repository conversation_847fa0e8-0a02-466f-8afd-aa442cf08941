import React, { memo, useCallback } from 'react';
import { useHistory, useRouteMatch } from 'react-router-dom';

import ContentPanel from '../../../common/components/containers/BasicModuleLayout/ContentPanel';
import useTranslation from '../../../common/components/utils/Translations/useTranslation';

import Box from '../../../common/components/other/Box';

const ProgramsSetupPage = () => {
  const { t } = useTranslation();
  const history = useHistory();
  const { url } = useRouteMatch();

  const renderCard = useCallback(
    ({ path, title }) => {
      const onClick = () => {
        history.push(`${url}/${path}`);
      };

      return (
        <Box
          key={`program-group-${path}`}
          href={`${url}/${path}`}
          title={t(title)}
          onClick={onClick}
        />
      );
    },
    [url, history, t],
  );

  const sections = [
    {
      path: 'program-group-type-setup',
      title: 'Program Group Type Setup',
    },
  ];

  return (
    <ContentPanel title={t('Programs Setup')}>
      {sections.map(renderCard)}
    </ContentPanel>
  );
};

export default memo(ProgramsSetupPage);
